import { defHttp } from '@/utils/http/axios'

// 查询积分商城订单列表
export function getPointProductOrderPage(params) {
  return defHttp.get({ url: '/pointMall/point-product-order/page', params })
}

// 查询积分商城订单详情
export function getPointProductOrder(id: number) {
  return defHttp.get({ url: `/pointMall/point-product-order/get?id=${id}` })
}

// 新增积分商城订单
export function createPointProductOrder(data) {
  return defHttp.post({ url: '/pointMall/point-product-order/create', data })
}

// 修改积分商城订单
export function updatePointProductOrder(data) {
  return defHttp.put({ url: '/pointMall/point-product-order/update', data })
}

// 删除积分商城订单
export function deletePointProductOrder(id: number) {
  return defHttp.delete({ url: `/pointMall/point-product-order/delete?id=${id}` })
}

// 导出积分商城订单 Excel
export function exportPointProductOrder(params) {
  return defHttp.download({ url: '/pointMall/point-product-order/export-excel', params }, '积分商城订单.xls')
}

// 积分商城订单发货
export function deliveryPointProductOrder(data) {
  return defHttp.put({ url: '/pointMall/point-product-order/delivery', data })
}

// 取消积分商城订单
export function cancelPointProductOrder(id: number) {
  return defHttp.put({ url: `/pointMall/point-product-order/cancel?id=${id}` })
}
